#!/usr/bin/env python3
"""
Test script for message deduplication functionality
"""
import os
import django
import requests
import json
import time

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from userbot.utils.message_deduplication import can_send_to_chat, mark_sent_to_chat, deduplicator

def test_deduplication_logic():
    """Test the deduplication logic directly"""
    print("=== Testing Deduplication Logic ===")
    
    user_id = 123456789
    chat_id = 987654321
    message_text = "Test message for deduplication"
    interval_minutes = 2  # 2 minutes for quick testing
    
    print(f"User ID: {user_id}")
    print(f"Chat ID: {chat_id}")
    print(f"Message: {message_text}")
    print(f"Interval: {interval_minutes} minutes")
    print()
    
    # Test 1: First send should be allowed
    print("Test 1: First send")
    can_send, reason = can_send_to_chat(user_id, chat_id, message_text, interval_minutes)
    print(f"Can send: {can_send}, Reason: {reason}")
    
    if can_send:
        mark_sent_to_chat(user_id, chat_id, message_text, interval_minutes)
        print("Message marked as sent")
    print()
    
    # Test 2: Immediate second send should be blocked
    print("Test 2: Immediate second send")
    can_send, reason = can_send_to_chat(user_id, chat_id, message_text, interval_minutes)
    print(f"Can send: {can_send}, Reason: {reason}")
    print()
    
    # Test 3: Different message should be allowed
    print("Test 3: Different message")
    different_message = "Different test message"
    can_send, reason = can_send_to_chat(user_id, chat_id, different_message, interval_minutes)
    print(f"Can send: {can_send}, Reason: {reason}")
    
    if can_send:
        mark_sent_to_chat(user_id, chat_id, different_message, interval_minutes)
        print("Different message marked as sent")
    print()
    
    # Test 4: Same message to different chat should be allowed
    print("Test 4: Same message to different chat")
    different_chat_id = 111222333
    can_send, reason = can_send_to_chat(user_id, different_chat_id, message_text, interval_minutes)
    print(f"Can send: {can_send}, Reason: {reason}")
    print()
    
    # Test 5: Cache stats
    print("Test 5: Cache stats")
    stats = deduplicator.get_cache_stats(user_id)
    print(f"Cache stats: {json.dumps(stats, indent=2)}")
    print()

def test_api_endpoint():
    """Test the API endpoint"""
    print("=== Testing API Endpoint ===")
    
    base_url = "http://127.0.0.1:8000"
    
    # Test data
    test_data = {
        "user_id": 123456789,
        "text": "API test message for deduplication",
        "interval_minutes": 1  # 1 minute for quick testing
    }
    
    print(f"Test data: {json.dumps(test_data, indent=2)}")
    print()
    
    # Test 1: First API call
    print("Test 1: First API call")
    try:
        response = requests.post(f"{base_url}/api/send-auto-message/", json=test_data)
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    print()
    
    # Test 2: Immediate second API call (should have skipped chats)
    print("Test 2: Immediate second API call")
    try:
        response = requests.post(f"{base_url}/api/send-auto-message/", json=test_data)
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    print()
    
    # Test 3: Clear cache
    print("Test 3: Clear cache")
    clear_data = {"user_id": test_data["user_id"]}
    try:
        response = requests.post(f"{base_url}/api/clear-message-cache/", json=clear_data)
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    print()

def main():
    """Main test function"""
    print("Django Cache-based Message Deduplication Test")
    print("=" * 50)
    print()
    
    # Test deduplication logic directly
    test_deduplication_logic()
    
    print("=" * 50)
    
    # Test API endpoint (requires Django server to be running)
    user_input = input("Do you want to test API endpoints? (Django server must be running) [y/N]: ")
    if user_input.lower() in ['y', 'yes']:
        test_api_endpoint()
    
    print("=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    main()
