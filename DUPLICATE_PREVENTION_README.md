# Duplicate Message Prevention System

Bu loyihada duplicate scheduled message larni oldini olish va tozalash tizimi implement qilindi.

## <PERSON><PERSON><PERSON> bir xil user_id va interval uchun bir nechta scheduled message yaratib, natijada bir guruhga bir nechta marta xabar yuborilayotgan edi.

## Yechim

### 1. Database Level Prevention

**Model o'zgarishlari (`userbot/models.py`):**
- `ScheduledMessage` modeliga unique constraint qo'shildi
- `get_active_for_user_and_interval()` class method qo'shildi
- `deactivate_and_cancel_task()` method qo'shildi

```python
constraints = [
    models.UniqueConstraint(
        fields=['user_id', 'interval_minutes'],
        condition=models.Q(is_active=True, status__in=['pending', 'sent']),
        name='unique_active_schedule_per_user_interval'
    )
]
```

### 2. API Level Prevention

**Views o'zgarishlari (`userbot/views.py`):**
- `create_scheduled_message` API da duplicate check qo'shildi
- Agar bir xil user_id va interval uchun active schedule mavjud bo'lsa:
  - Agar text bir xil bo'lsa - mavjud schedule qaytariladi
  - Agar text boshqa bo'lsa - eski schedule deactivate qilinadi va yangi yaratiladi

### 3. Task Level Prevention

**Task o'zgarishlari (`userbot/tasks.py`):**
- `send_scheduled_message` task da scheduled message active ekanligini tekshirish qo'shildi
- Agar message inactive yoki cancelled bo'lsa - task skip qilinadi

### 4. Utility Functions

**Duplicate Prevention Utils (`userbot/utils/duplicate_prevention.py`):**
- `cleanup_duplicate_tasks()` - duplicate task larni tozalash
- `cleanup_orphaned_periodic_tasks()` - orphaned task larni tozalash
- `get_duplicate_statistics()` - duplicate statistikalarini olish

### 5. Management Command

**Django Management Command (`userbot/management/commands/cleanup_duplicates.py`):**
```bash
# Barcha duplicate larni tozalash
python manage.py cleanup_duplicates

# Faqat statistikalarni ko'rish
python manage.py cleanup_duplicates --stats-only

# Dry run (hech narsa o'zgartirmasdan ko'rish)
python manage.py cleanup_duplicates --dry-run

# Muayyan user uchun tozalash
python manage.py cleanup_duplicates --user-id 123456789
```

### 6. API Endpoints

#### Duplicate Statistics
```http
GET /api/get-duplicate-stats/
```

Response:
```json
{
    "success": true,
    "statistics": {
        "duplicate_groups": 2,
        "total_duplicate_messages": 3,
        "details": [
            {
                "user_id": 123456789,
                "interval_minutes": 60,
                "count": 2
            }
        ]
    }
}
```

#### Cleanup Duplicates
```http
POST /api/cleanup-duplicate-schedules/
Content-Type: application/json

{
    "user_id": 123456789,  // optional
    "dry_run": false       // optional, default false
}
```

Response:
```json
{
    "success": true,
    "message": "Cleanup completed. Total items cleaned: 3",
    "statistics": {...},
    "cleanup_results": [
        {
            "user_id": 123456789,
            "interval_minutes": 60,
            "cleaned_count": 1
        }
    ],
    "total_cleaned": 3
}
```

## Qanday Ishlaydi

### Yangi Schedule Yaratishda:
1. API duplicate check qiladi
2. Agar mavjud active schedule topilsa:
   - Text bir xil bo'lsa - mavjud qaytariladi
   - Text boshqa bo'lsa - eski deactivate qilinadi
3. Yangi schedule yaratiladi
4. Database constraint duplicate yaratishni oldini oladi

### Task Execution da:
1. Task ishga tushganda scheduled message hali active ekanligini tekshiradi
2. Agar inactive bo'lsa - task skip qilinadi
3. Agar active bo'lsa - xabar yuboriladi

### Cleanup Process:
1. Duplicate detection - bir xil user_id va interval uchun bir nechta active message
2. Eng eski message saqlanadi, qolganlari deactivate qilinadi
3. Orphaned periodic task lar ham tozalanadi

## Migration

```bash
python manage.py makemigrations userbot --name add_duplicate_prevention
python manage.py migrate
```

## Monitoring

### Duplicate larni tekshirish:
```bash
# Statistikalarni ko'rish
python manage.py cleanup_duplicates --stats-only

# API orqali
curl -X GET http://localhost:8000/api/get-duplicate-stats/
```

### Muntazam tozalash:
Cron job yoki Celery periodic task sifatida qo'shish mumkin:
```bash
# Har kuni soat 2:00 da
0 2 * * * cd /path/to/project && python manage.py cleanup_duplicates
```

## Xavfsizlik

- Barcha o'zgarishlar transaction ichida amalga oshiriladi
- Dry run mode mavjud
- Detailed logging
- Error handling va rollback

## Test Qilish

```bash
# Test duplicate yaratish
curl -X POST http://localhost:8000/api/create-scheduled-message/ \
  -H "Content-Type: application/json" \
  -d '{"user_id": 123, "text": "Test", "interval_minutes": 60}'

# Bir xil parametrlar bilan yana bir marta
curl -X POST http://localhost:8000/api/create-scheduled-message/ \
  -H "Content-Type: application/json" \
  -d '{"user_id": 123, "text": "Test", "interval_minutes": 60}'

# Statistikalarni tekshirish
curl -X GET http://localhost:8000/api/get-duplicate-stats/

# Tozalash
curl -X POST http://localhost:8000/api/cleanup-duplicate-schedules/ \
  -H "Content-Type: application/json" \
  -d '{"dry_run": true}'
```
