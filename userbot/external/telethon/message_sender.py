#!/usr/bin/env python3
"""
Telethon Message Sender for AUTO folder functionality
"""
import os
import django
import asyncio
import logging
from typing import List, Dict, Any

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from telethon import TelegramClient, types
from telethon.tl.functions.messages import GetDialogFiltersRequest
from telethon.errors import FloodWaitError
from userbot.models import Session
from asgiref.sync import sync_to_async

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TelethonMessageSender:
    """
    Class for sending messages to AUTO folder chats using Telethon
    """
    
    def __init__(self):
        self.client = None
        self.session_data = None
    
    async def connect_session(self, user_id: int) -> bool:
        """
        Connect to Telegram session based on user_id
        
        Args:
            user_id: Telegram user ID
            
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Get session data from database
            self.session_data = await sync_to_async(
                Session.objects.filter(user_id=user_id, is_active=True).first
            )()
            
            if not self.session_data:
                logger.error(f"No active session found for user_id: {user_id}")
                return False
            
            # Create Telethon client
            self.client = TelegramClient(
                self.session_data.session_name,
                self.session_data.api_id,
                self.session_data.api_hash
            )
            
            # Connect to Telegram
            await self.client.connect()
            
            if not await self.client.is_user_authorized():
                logger.error(f"Session not authorized for user_id: {user_id}")
                await self.client.disconnect()
                return False
            
            # Get user info for logging
            me = await self.client.get_me()
            logger.info(f"Connected as: {me.first_name} (@{me.username or 'no_username'}) ID: {me.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error connecting session for user_id {user_id}: {e}")
            if self.client:
                await self.client.disconnect()
            return False
    
    async def get_dialog_filters(self) -> List:
        """
        Get dialog filters (folders) from Telegram
        
        Returns:
            List of dialog filters
        """
        try:
            result = await self.client(GetDialogFiltersRequest())
            return result.filters
        except Exception as e:
            logger.error(f"Error getting dialog filters: {e}")
            return []
    
    async def get_auto_folder_chats(self) -> List[int]:
        """
        Get chat IDs from AUTO folder using Telegram's folder system
        
        Returns:
            List of chat IDs in AUTO folder
        """
        auto_chats = []
        
        try:
            filters = await self.get_dialog_filters()
            
            auto_filter = None
            for filter_obj in filters:
                if hasattr(filter_obj, 'title') and 'AUTO' in filter_obj.title.upper():
                    auto_filter = filter_obj
                    logger.info(f"Found AUTO folder: {filter_obj.title}")
                    break
            
            if auto_filter and hasattr(auto_filter, 'include_peers'):
                for peer in auto_filter.include_peers:
                    if isinstance(peer, types.InputPeerChat):
                        auto_chats.append(-peer.chat_id)  # Group chat ID
                    elif isinstance(peer, types.InputPeerChannel):
                        auto_chats.append(-1000000000000 - peer.channel_id)  # Channel/supergroup ID
                    elif isinstance(peer, types.InputPeerUser):
                        auto_chats.append(peer.user_id)  # User ID
                
                logger.info(f"Found {len(auto_chats)} chats in AUTO folder")
            else:
                logger.warning("AUTO folder not found")
            
            return auto_chats
            
        except Exception as e:
            logger.error(f"Error getting AUTO folder chats: {e}")
            return []
    
    async def send_messages_to_auto_folder(self, message_text: str) -> Dict[str, Any]:
        """
        Send message to all chats in AUTO folder
        
        Args:
            message_text: Text message to send
            
        Returns:
            Dict with results of message sending
        """
        results = {
            'success': [],
            'failed': [],
            'total_chats': 0,
            'sent_count': 0,
            'failed_count': 0
        }
        
        try:
            # Get AUTO folder chats
            auto_chats = await self.get_auto_folder_chats()
            results['total_chats'] = len(auto_chats)
            
            if not auto_chats:
                logger.warning("No chats found in AUTO folder")
                return results
            
            # Send message to each chat
            for chat_id in auto_chats:
                message_sent = False

                try:
                    # Try to get the entity first to ensure it's accessible
                    try:
                        entity = await self.client.get_entity(chat_id)
                        await self.client.send_message(entity, message_text)
                        message_sent = True
                        logger.info(f"Message sent to chat {chat_id}")
                    except FloodWaitError as e:
                        logger.warning(f"Flood wait for {e.seconds} seconds on chat {chat_id}")
                        await asyncio.sleep(e.seconds)
                        # Retry after flood wait
                        await self.client.send_message(entity, message_text)
                        message_sent = True
                        logger.info(f"Message sent to chat {chat_id} (after flood wait)")
                    except Exception:
                        # If entity not found, try with chat_id directly
                        try:
                            await self.client.send_message(chat_id, message_text)
                            message_sent = True
                            logger.info(f"Message sent to chat {chat_id} (direct)")
                        except FloodWaitError as e:
                            logger.warning(f"Flood wait for {e.seconds} seconds on chat {chat_id} (direct)")
                            await asyncio.sleep(e.seconds)
                            # Retry after flood wait
                            await self.client.send_message(chat_id, message_text)
                            message_sent = True
                            logger.info(f"Message sent to chat {chat_id} (direct, after flood wait)")

                    # If message was sent successfully, add to results
                    if message_sent:
                        results['success'].append(chat_id)
                        results['sent_count'] += 1

                    # Small delay to avoid flood limits
                    await asyncio.sleep(1)

                except Exception as e:
                    results['failed'].append({'chat_id': chat_id, 'error': str(e)})
                    results['failed_count'] += 1
                    logger.error(f"Error sending message to chat {chat_id}: {e}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in send_messages_to_auto_folder: {e}")
            results['failed'].append({'error': str(e)})
            results['failed_count'] += 1
            return results
    
    async def disconnect(self):
        """
        Disconnect from Telegram session
        """
        if self.client and self.client.is_connected():
            await self.client.disconnect()
            logger.info(f"Disconnected client for user_id: {self.session_data.user_id if self.session_data else 'unknown'}")


async def send_message_to_auto_folder(user_id: int, text: str) -> Dict[str, Any]:
    """
    Main function to send message to AUTO folder chats
    
    Args:
        user_id: Telegram user ID
        text: Message text to send
        
    Returns:
        Dict with success status and results
    """
    sender = TelethonMessageSender()
    
    try:
        # Connect to session
        if not await sender.connect_session(user_id):
            return {
                'success': False,
                'error': f'Failed to connect session for user_id: {user_id}',
                'results': {}
            }
        
        # Send messages
        results = await sender.send_messages_to_auto_folder(text)
        
        return {
            'success': True,
            'message': f'Message sending completed for user_id: {user_id}',
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Error in send_message_to_auto_folder: {e}")
        return {
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'results': {}
        }
    
    finally:
        # Always disconnect
        await sender.disconnect()
