"""
Django management command to clean up duplicate scheduled messages and tasks
"""
from django.core.management.base import BaseCommand
from django.db.models import Count
from userbot.models import ScheduledMessage
from userbot.utils.duplicate_prevention import (
    cleanup_duplicate_tasks,
    cleanup_orphaned_periodic_tasks,
    get_duplicate_statistics
)


class Command(BaseCommand):
    help = 'Clean up duplicate scheduled messages and orphaned periodic tasks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleaned up without actually doing it',
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='Clean up duplicates for specific user only',
        )
        parser.add_argument(
            '--stats-only',
            action='store_true',
            help='Only show statistics without cleaning up',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        user_id = options['user_id']
        stats_only = options['stats_only']

        self.stdout.write(
            self.style.SUCCESS('Starting duplicate cleanup process...')
        )

        # Show statistics first
        stats = get_duplicate_statistics()
        if stats['success']:
            self.stdout.write(
                f"Found {stats['duplicate_groups']} groups with duplicates"
            )
            self.stdout.write(
                f"Total duplicate messages: {stats['total_duplicate_messages']}"
            )
            
            if stats['details']:
                self.stdout.write("\nDuplicate details:")
                for detail in stats['details']:
                    self.stdout.write(
                        f"  User {detail['user_id']}, "
                        f"Interval {detail['interval_minutes']}min: "
                        f"{detail['count']} messages"
                    )
        else:
            self.stdout.write(
                self.style.ERROR(f"Error getting statistics: {stats['error']}")
            )

        if stats_only:
            return

        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )

        total_cleaned = 0

        # Clean up duplicates for specific user or all users
        if user_id:
            # Clean up for specific user
            intervals = ScheduledMessage.objects.filter(
                user_id=user_id,
                is_active=True
            ).values_list('interval_minutes', flat=True).distinct()
            
            for interval in intervals:
                if not dry_run:
                    result = cleanup_duplicate_tasks(user_id, interval)
                    if result['success']:
                        cleaned_count = result['cleaned_count']
                        total_cleaned += cleaned_count
                        if cleaned_count > 0:
                            self.stdout.write(
                                f"Cleaned {cleaned_count} duplicates for "
                                f"user {user_id}, interval {interval}min"
                            )
                    else:
                        self.stdout.write(
                            self.style.ERROR(
                                f"Error cleaning user {user_id}, "
                                f"interval {interval}: {result['error']}"
                            )
                        )
                else:
                    self.stdout.write(
                        f"Would clean duplicates for user {user_id}, "
                        f"interval {interval}min"
                    )
        else:
            # Clean up for all users
            duplicates = ScheduledMessage.objects.filter(
                is_active=True,
                status__in=['pending', 'sent']
            ).values('user_id', 'interval_minutes').annotate(
                count=Count('id')
            ).filter(count__gt=1)

            for duplicate in duplicates:
                user_id = duplicate['user_id']
                interval = duplicate['interval_minutes']
                
                if not dry_run:
                    result = cleanup_duplicate_tasks(user_id, interval)
                    if result['success']:
                        cleaned_count = result['cleaned_count']
                        total_cleaned += cleaned_count
                        if cleaned_count > 0:
                            self.stdout.write(
                                f"Cleaned {cleaned_count} duplicates for "
                                f"user {user_id}, interval {interval}min"
                            )
                    else:
                        self.stdout.write(
                            self.style.ERROR(
                                f"Error cleaning user {user_id}, "
                                f"interval {interval}: {result['error']}"
                            )
                        )
                else:
                    self.stdout.write(
                        f"Would clean {duplicate['count'] - 1} duplicates for "
                        f"user {user_id}, interval {interval}min"
                    )

        # Clean up orphaned periodic tasks
        if not dry_run:
            orphan_result = cleanup_orphaned_periodic_tasks()
            if orphan_result['success']:
                orphan_count = orphan_result['cleaned_count']
                total_cleaned += orphan_count
                if orphan_count > 0:
                    self.stdout.write(
                        f"Cleaned {orphan_count} orphaned periodic tasks"
                    )
            else:
                self.stdout.write(
                    self.style.ERROR(
                        f"Error cleaning orphaned tasks: {orphan_result['error']}"
                    )
                )
        else:
            self.stdout.write("Would clean orphaned periodic tasks")

        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Cleanup completed. Total items cleaned: {total_cleaned}'
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING('Dry run completed. No changes made.')
            )
