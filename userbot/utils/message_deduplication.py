"""
Simple cache-based message deduplication logic
"""
import hashlib
import logging
from datetime import datetime, timedelta
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)


class MessageDeduplicator:
    """
    Simple cache-based message deduplication to prevent sending duplicate messages
    to the same chat within a specified interval
    """
    
    def __init__(self, default_interval_minutes=60):
        """
        Initialize deduplicator
        
        Args:
            default_interval_minutes (int): Default interval in minutes to prevent duplicates
        """
        self.default_interval_minutes = default_interval_minutes
        self.cache_prefix = "msg_dedup"
    
    def _get_cache_key(self, user_id, chat_id, message_hash=None):
        """
        Generate cache key for chat-user combination

        Args:
            user_id (int): Telegram user ID
            chat_id (int): Chat ID
            message_hash (str, optional): Hash of message content

        Returns:
            str: Cache key (format: msg_dedup:chat_id:user_id[:message_hash])
        """
        if message_hash:
            return f"{self.cache_prefix}:{chat_id}:{user_id}:{message_hash}"
        return f"{self.cache_prefix}:{chat_id}:{user_id}"
    
    def _get_message_hash(self, message_text):
        """
        Generate hash for message content
        
        Args:
            message_text (str): Message text
            
        Returns:
            str: MD5 hash of message
        """
        return hashlib.md5(message_text.encode('utf-8')).hexdigest()[:12]
    
    def can_send_message(self, user_id, chat_id, message_text, interval_minutes=None):
        """
        Check if message can be sent to chat (not duplicate within interval)
        
        Args:
            user_id (int): Telegram user ID
            chat_id (int): Chat ID
            message_text (str): Message text
            interval_minutes (int, optional): Interval in minutes, uses default if not provided
            
        Returns:
            tuple: (can_send: bool, reason: str)
        """
        if interval_minutes is None:
            interval_minutes = self.default_interval_minutes
        
        try:
            message_hash = self._get_message_hash(message_text)
            cache_key = self._get_cache_key(user_id, chat_id, message_hash)
            
            # Check if same message was sent recently
            last_sent = cache.get(cache_key)
            
            if last_sent:
                last_sent_time = datetime.fromisoformat(last_sent)
                time_diff = timezone.now() - last_sent_time
                
                if time_diff < timedelta(minutes=interval_minutes):
                    remaining_minutes = interval_minutes - (time_diff.total_seconds() / 60)
                    return False, f"Same message sent {time_diff.total_seconds():.0f}s ago. Wait {remaining_minutes:.1f} more minutes."
            
            return True, "OK"
            
        except Exception as e:
            logger.error(f"Error checking message deduplication: {e}")
            # On error, allow sending to avoid blocking
            return True, "Error in deduplication check, allowing send"
    
    def mark_message_sent(self, user_id, chat_id, message_text, interval_minutes=None):
        """
        Mark message as sent for deduplication tracking
        
        Args:
            user_id (int): Telegram user ID
            chat_id (int): Chat ID
            message_text (str): Message text
            interval_minutes (int, optional): Interval in minutes, uses default if not provided
        """
        if interval_minutes is None:
            interval_minutes = self.default_interval_minutes
        
        try:
            message_hash = self._get_message_hash(message_text)
            cache_key = self._get_cache_key(user_id, chat_id, message_hash)
            
            # Store current timestamp with expiry slightly longer than interval
            cache_timeout = (interval_minutes + 5) * 60  # Add 5 minutes buffer
            cache.set(cache_key, timezone.now().isoformat(), timeout=cache_timeout)
            
            logger.debug(f"Marked message as sent: user={user_id}, chat={chat_id}, hash={message_hash}")
            
        except Exception as e:
            logger.error(f"Error marking message as sent: {e}")
    
    def clear_chat_cache(self, user_id, chat_id):
        """
        Clear all cached messages for a specific chat-user combination

        Args:
            user_id (int): Telegram user ID
            chat_id (int): Chat ID
        """
        try:
            # Since Django cache doesn't support pattern deletion easily,
            # we'll use a simple approach with a known pattern
            base_key = self._get_cache_key(user_id, chat_id)

            # Try to delete common patterns (this is a simple approach)
            # In production, you might want to use Redis with pattern deletion
            for i in range(100):  # Try common hash patterns
                test_key = f"{base_key}:{i:02d}"
                cache.delete(test_key)

            logger.info(f"Cleared cache for chat={chat_id}, user={user_id}")

        except Exception as e:
            logger.error(f"Error clearing chat cache: {e}")
    
    def get_cache_stats(self, user_id):
        """
        Get cache statistics for a user (limited functionality with Django cache)
        
        Args:
            user_id (int): Telegram user ID
            
        Returns:
            dict: Basic stats
        """
        try:
            # Django's cache backend doesn't provide easy stats
            # This is a placeholder for basic info
            return {
                'user_id': user_id,
                'cache_prefix': self.cache_prefix,
                'default_interval_minutes': self.default_interval_minutes,
                'note': 'Detailed stats not available with Django database cache'
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {'error': str(e)}


# Global deduplicator instance
deduplicator = MessageDeduplicator()


def can_send_to_chat(user_id, chat_id, message_text, interval_minutes=60):
    """
    Convenience function to check if message can be sent
    
    Args:
        user_id (int): Telegram user ID
        chat_id (int): Chat ID
        message_text (str): Message text
        interval_minutes (int): Interval in minutes
        
    Returns:
        tuple: (can_send: bool, reason: str)
    """
    return deduplicator.can_send_message(user_id, chat_id, message_text, interval_minutes)


def mark_sent_to_chat(user_id, chat_id, message_text, interval_minutes=60):
    """
    Convenience function to mark message as sent
    
    Args:
        user_id (int): Telegram user ID
        chat_id (int): Chat ID
        message_text (str): Message text
        interval_minutes (int): Interval in minutes
    """
    deduplicator.mark_message_sent(user_id, chat_id, message_text, interval_minutes)
