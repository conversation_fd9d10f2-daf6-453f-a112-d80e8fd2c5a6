"""
Utility functions for preventing duplicate scheduled messages and tasks
"""
import logging
from django.db import transaction
from django_celery_beat.models import PeriodicTask
from userbot.models import ScheduledMessage

logger = logging.getLogger(__name__)


def cleanup_duplicate_tasks(user_id, interval_minutes):
    """
    Clean up duplicate periodic tasks for the same user and interval
    
    Args:
        user_id (int): Telegram user ID
        interval_minutes (int): Interval in minutes
    
    Returns:
        dict: Cleanup results
    """
    try:
        with transaction.atomic():
            # Find all scheduled messages for this user and interval
            scheduled_messages = ScheduledMessage.objects.filter(
                user_id=user_id,
                interval_minutes=interval_minutes,
                is_active=True
            ).order_by('created_at')
            
            if len(scheduled_messages) <= 1:
                return {
                    'success': True,
                    'message': 'No duplicates found',
                    'cleaned_count': 0
                }
            
            # Keep the first one, deactivate others
            keep_message = scheduled_messages.first()
            duplicate_messages = scheduled_messages[1:]
            
            cleaned_count = 0
            for duplicate in duplicate_messages:
                # Cancel associated periodic task
                if duplicate.celery_task_id:
                    try:
                        PeriodicTask.objects.filter(
                            name=duplicate.celery_task_id
                        ).update(enabled=False)
                        logger.info(f"Disabled periodic task: {duplicate.celery_task_id}")
                    except Exception as e:
                        logger.error(f"Error disabling task {duplicate.celery_task_id}: {e}")
                
                # Deactivate scheduled message
                duplicate.is_active = False
                duplicate.status = 'cancelled'
                duplicate.save()
                cleaned_count += 1
                
                logger.info(f"Deactivated duplicate scheduled message: {duplicate.id}")
            
            return {
                'success': True,
                'message': f'Cleaned up {cleaned_count} duplicate tasks',
                'cleaned_count': cleaned_count,
                'kept_message_id': keep_message.id
            }
            
    except Exception as e:
        logger.error(f"Error cleaning up duplicates for user {user_id}: {e}")
        return {
            'success': False,
            'error': str(e),
            'cleaned_count': 0
        }


def cleanup_orphaned_periodic_tasks():
    """
    Clean up periodic tasks that don't have corresponding active scheduled messages
    
    Returns:
        dict: Cleanup results
    """
    try:
        # Find all periodic tasks for scheduled messages
        periodic_tasks = PeriodicTask.objects.filter(
            task='userbot.tasks.send_scheduled_message',
            enabled=True
        )
        
        cleaned_count = 0
        for task in periodic_tasks:
            try:
                # Extract scheduled_message_id from task name
                if task.name.startswith('scheduled_message_'):
                    parts = task.name.split('_')
                    if len(parts) >= 3:
                        scheduled_message_id = int(parts[2])
                        
                        # Check if corresponding scheduled message exists and is active
                        try:
                            scheduled_msg = ScheduledMessage.objects.get(
                                id=scheduled_message_id,
                                is_active=True
                            )
                        except ScheduledMessage.DoesNotExist:
                            # Orphaned task - disable it
                            task.enabled = False
                            task.save()
                            cleaned_count += 1
                            logger.info(f"Disabled orphaned periodic task: {task.name}")
                            
            except (ValueError, IndexError) as e:
                logger.warning(f"Could not parse task name {task.name}: {e}")
                continue
        
        return {
            'success': True,
            'message': f'Cleaned up {cleaned_count} orphaned tasks',
            'cleaned_count': cleaned_count
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up orphaned tasks: {e}")
        return {
            'success': False,
            'error': str(e),
            'cleaned_count': 0
        }


def get_duplicate_statistics():
    """
    Get statistics about duplicate scheduled messages
    
    Returns:
        dict: Statistics
    """
    try:
        from django.db.models import Count
        
        # Find users with multiple active scheduled messages for same interval
        duplicates = ScheduledMessage.objects.filter(
            is_active=True,
            status__in=['pending', 'sent']
        ).values('user_id', 'interval_minutes').annotate(
            count=Count('id')
        ).filter(count__gt=1)
        
        total_duplicates = sum(item['count'] - 1 for item in duplicates)
        
        return {
            'success': True,
            'duplicate_groups': len(duplicates),
            'total_duplicate_messages': total_duplicates,
            'details': list(duplicates)
        }
        
    except Exception as e:
        logger.error(f"Error getting duplicate statistics: {e}")
        return {
            'success': False,
            'error': str(e)
        }
